"use client";

import type React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>le,
  <PERSON>alog<PERSON>ontent,
  <PERSON>,
  Typography,
  IconButton,
} from "@mui/material";
import Image from "next/image";
import "./SystemReliabilityDialog.scss";
import { SystemReliablityIcon, SystemReliablityIconButton } from "@/public";

interface Section {
  title: string;
  description: string | string[];
}

interface TestingContent {
  title: string;
  icon: any;
  sections: Section[];
}

interface SystemReliabilityDialogProps {
  open: boolean;
  onClose: () => void;
  contentType?:
    | "system-reliability"
    | "communication"
    | "software"
    | "simulation";
}

const SystemReliabilityDialog: React.FC<SystemReliabilityDialogProps> = ({
  open,
  onClose,
  contentType = "system-reliability",
}) => {
  // Define content for different testing types
  const getTestingContent = (type: string): TestingContent => {
    switch (type) {
      case "communication":
        return {
          title: "Communication Testing",
          icon: SystemReliablityIcon,
          sections: [
            {
              title: "Connectivity Testing",
              description:
                "Ensuring the device can establish and maintain healthy connection in all weather conditions. Verifying that system design and performance as expected under various environmental conditions, signal strength, and loss recovery.",
            },
            {
              title: "Interoperability Testing",
              description:
                "Ensures devices can communicate with other systems, platforms, or devices using different protocols or vendors. Cross-Device Compatibility testing across different hardware and firmware configurations.",
            },
            {
              title: "Latency and Throughput Testing",
              description:
                "Aadvik team ensures reliable communication with consistent latency, maintaining predefined time intervals and data rates for data exchange between systems. This enables accurate evaluation of the system under test in terms of responsiveness and capacity.",
            },
            {
              title: "Security Testing",
              description:
                "Executing comprehensive safety of the data security by comprehensively enforcing it through robust encryption, authentication, and protective measures to safeguard against any external kind of cyber threats.",
            },
            {
              title: "Protocol Compliance Testing",
              description:
                "We offer protocol compliance testing services to ensure your devices and systems strictly adhere to industry communication standards. This testing is essential for achieving interoperability, certification, and reliable performance across multi-vendor environments.",
            },
            {
              title: "Power Consumption Test",
              description:
                "Assesses of power consumption need key engineering skillset to achieve the reliable test results. We assess communication frequency and mode affect battery life which is a crucial part for any low-power IoT devices.",
            },
          ],
        };

      case "software":
        return {
          title: "Software Testing",
          icon: SystemReliablityIcon,
          sections: [
            {
              title: "Comprehensive Software Testing Services",
              description:
                "Aadvik TekLabs offers comprehensive software testing services to ensure your product is reliable, secure, and compliant with relevant standards. Our testing processes cover functionality, performance, compatibility, and security, helping you deliver a high-quality product to the market.",
            },
            {
              title: "Actionable Insights for Better Products",
              description:
                "Aadvik TekLabs provides actionable insights that drive smarter design decisions and enhance product longevity",
            },
            {
              title: "Our General standard Software Testing Process involve",
              description: [
                "Understand and analyze software requirements & Identify testable elements.",
                "Define and creation of test plan, testing strategy, scope, objectives, and resources.",
                "Develop detailed test cases and test scripts based on requirements.",
                "Creating test environment & setup for hardware, software, and network under test",
                "Test execution and defect reporting to the development team for resolution",
                "Regression testing and closure of tickets with final test results and report release.",
              ],
            },
          ],
        };

      case "simulation":
        return {
          title: "Simulation Testing",
          icon: SystemReliablityIcon,
          sections: [
            {
              title: "Virtual Simulation Environment",
              description:
                "Aadvik testing team builds a virtual simulation environment tailored to your use case, enabling the replication of real-world interactions.",
            },
            {
              title: "Business-Centric Approach",
              description:
                "Our business centric approach allows us to validate core system functionality and performance well in advance before actual field deployment.",
            },
            {
              title: "Unit Testing",
              description:
                "Our engineers involves in testing each software modules along with the Hardware blocks in isolation to ensure their functionality.",
            },
            {
              title: "Integration Testing",
              description:
                "Our focused approach to verify interrelation between different software modules and subsystems and ensure smooth integration and inter-operability.",
            },
            {
              title: "System Testing",
              description:
                "Evaluates the overall operation, performance, and behavior of the entire embedded system, including its hardware and software components.",
            },
            {
              title: "Hardware Testing",
              description:
                "Aadvik TekLabs Hardware team work over reliability and functional testing of hardware peripherals and components such as sensors, actuators, and communication interfaces.",
            },
            {
              title: "Functional Simulation",
              description:
                "We performs focused simulation testing on verifying the functionality of the embedded software and runs test & simulation programs on a virtual model of the hardware",
            },
            {
              title: "Co-simulation Test",
              description:
                "Focusing on Firmware-hardware integration, timing, and logic behavior, Aadvik Team runs hardware and software models together to validate interactions between firmware and physical devices.",
            },
          ],
        };

      default:
        return {
          title: "System Reliability Testing",
          icon: SystemReliablityIcon,
          sections: [
            {
              title: "System Function Testing",
              description:
                "Ensuring comprehensive performance and validation of both hardware and software interactions in your design. Verifying that system design performance as expected under various environmental conditions and use cases.",
            },
            {
              title: "Product Reliability Assessments",
              description:
                "Evaluating the long-term durability and operational stability of your embedded system in real-world environments. Stress testing under extreme conditions to simulate and identify potential failure points.",
            },
            {
              title: "EMI & EMC Testing",
              description:
                "Our team ensure that the developed embedded system does not emit harmful electromagnetic interference (EMI) that could affect other devices or systems and validating that the system is immune to external electromagnetic disturbances, maintaining stable and reliable operation in field environments.",
            },
            {
              title: "Safety Assessment",
              description:
                "Executing comprehensive safety analysis to verify whether the product design meet international standards to ensuring your product is safe for use in the target market. Identifying and mitigating potential hazards, including electrical safety, thermal management, and operational safety in normal and fault conditions.",
            },
            {
              title: "Compliance Testing",
              description:
                "We ensure full compliance with Global Standards by ensuring your product meets key industry certifications and regulatory requirements, including ISO, IEC, UL, CE, FCC and RoHS. We ensure your product adheres to safety, electromagnetic compatibility, and environmental standards.",
            },
            {
              title: "Reliability Engineering & Support",
              description:
                "Failure Mode and Effects Analysis (FMEA) to anticipate and mitigate potential issues. Ongoing post-production support for system updates, system failures, and continuous reliability monitoring in the field.",
            },
          ],
        };
    }
  };

  const currentContent = getTestingContent(contentType);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      className="system-reliability-dialog"
      slotProps={{
        paper: {
          className: "dialog-paper",
        },
      }}
    >
      <DialogTitle className="dialog-title">
        <Box className="title-content">
          <Image
            src={currentContent.icon}
            alt="testing icon"
            width={42}
            height={45}
          />
          <Typography className="title">{currentContent.title}</Typography>
        </Box>
        <IconButton onClick={onClose}>
          <Image src={SystemReliablityIconButton} alt="close" />
        </IconButton>
      </DialogTitle>

      <DialogContent className="dialog-content">
        <div className="content-grid">
          {currentContent.sections.map((section: Section, index: number) => (
            <div key={index} className="section">
              <h3 className="section-title">{section.title}</h3>
              <div className="section-description">
                {Array.isArray(section.description) ? (
                  <ul style={{ paddingLeft: "20px", margin: 0 }}>
                    {section.description.map(
                      (item: string, itemIndex: number) => (
                        <li key={itemIndex} style={{ marginBottom: "8px" }}>
                          {item}
                        </li>
                      )
                    )}
                  </ul>
                ) : (
                  <p style={{ margin: 0 }}>{section.description}</p>
                )}
              </div>
            </div>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SystemReliabilityDialog;
