import { Box, Typography } from "@mui/material";
import "./WhyUs.scss";
import {
  why_us_icon1,
  why_us_icon2,
  why_us_icon3,
  why_us_icon4,
  why_us_icon5,
  why_us_icon6,
} from "@/public/index";
import Image from "next/image";

const features = [
  { label: "Comprehensive Approach", icon: why_us_icon1, active: true },
  { label: "Industry Experience", icon: why_us_icon2 },
  { label: "Affordable Services", icon: why_us_icon3 },
  { label: "Deep tech Expertise", icon: why_us_icon4 },
  { label: "Agile & Adaptive team", icon: why_us_icon5 },
  { label: "Inherent Quality Services", icon: why_us_icon6 },
];

const WhyUs = () => {
  return (
    <section className="why-us-section">
      <Typography className="section-title">Why us ?</Typography>
      <Typography className="section-description">
        Whether you're scaling operations, enhancing customer experiences, or
        driving digital transformation, we are the ideal partner to meet your
        technological needs—fully aligned with your business goals.
      </Typography>

      <Box className="features-grid">
        {features.map((item, index) => (
          <Box
            key={index}
            className={`feature-item ${item.active ? "active" : ""}`}
          >
            <Box className="icon-wrapper">
              <Image
                src={item.icon}
                alt="icon"
                className="icon"
                style={{ objectFit: "contain" }}
              />
            </Box>
            <Typography className="label">{item.label}</Typography>
          </Box>
        ))}
      </Box>
    </section>
  );
};

export default WhyUs;
