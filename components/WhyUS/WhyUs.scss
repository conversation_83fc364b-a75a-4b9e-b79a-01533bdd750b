.why-us-section {
  background-color: #f6f6f6;
  color: #d6dce3;
  padding: 80px 5%;
  text-align: center;
  font-family: "Inter", sans-serif;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  .section-title {
    font-family: Inter;
    font-weight: 700;
    font-size: 40px;

    letter-spacing: 0%;
    text-align: center;
    padding-bottom: 20px;
    border-bottom: 0.5px solid #021f2e;
    width: 100%;
    color: #021f2e;
  }

  .section-description {
    font-family: Montserrat;
    font-weight: 400;
    font-size: 16px;
    leading-trim: Cap height;

    letter-spacing: 0%;
    width: 100%;
    max-width: 600px;
    padding-top: 52px;
    color: #021f2e;
  }

  .features-grid {
    padding-top: 50px;
    display: grid;
    grid-template-columns: repeat(3, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 60px;
    width: 100%;

    .feature-item {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding: 32px 32px;
      border-radius: 8px;
      transition: background-color 0.3s ease;
      gap: 16px;
      .icon-wraper {
        width: 93px;
        height: 74px;

        .icon {
          width: 100%;
          height: 100%;
          margin-bottom: 10px;
        }
      }

      .label {
        font-family: Montserrat;
        font-weight: 400;
        font-size: 24px;

        letter-spacing: 0%;
        vertical-align: middle;
        color: #021f2e;

        b,
        span {
          color: #ffffff;
        }
      }

      &:hover {
        background: #d77d462b;

        .label {
          font-family: Montserrat;
          font-weight: 600;
          font-style: italic;
          font-size: 24px;

          letter-spacing: 0%;
          vertical-align: middle;
          color: #021f2e;
        }
      }
    }
  }

  @media (max-width: 1400px) {
    .features-grid {
      grid-template-columns: repeat(3, minmax(220px, 1fr));
      gap: 16px;
    }

    .feature-item {
      padding: 24px 20px;
    }
  }

  @media (max-width: 1200px) {
    .features-grid {
      grid-template-columns: repeat(2, minmax(200px, 1fr));
      gap: 14px;
    }

    .feature-item {
      padding: 18px 12px;
    }
  }

  @media (max-width: 800px) {
    .features-grid {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .feature-item {
      align-items: center;
      padding: 14px 8px;
    }
  }

  .bottom-text-container {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    border-top: 0.5px solid #418fba;
    border-bottom: 0.5px solid #418fba;
  }

  .bottom-text {
    font-family: Inter;
    font-weight: 800;
    font-size: 48px;
    leading-trim: Cap height;

    letter-spacing: 0%;
    text-align: center;
    max-width: 900px;

    padding: 93px 0px 68px;

    .highlight {
      background: linear-gradient(90deg, #6bd0b9 10.58%, #404a78 62.02%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .banner-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background-size: cover;
    background-position: center;
    opacity: 0.1;
    filter: blur(10px);
    pointer-events: none;
  }
}
