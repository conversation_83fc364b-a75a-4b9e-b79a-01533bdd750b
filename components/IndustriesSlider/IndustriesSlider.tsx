"use client";

import React from "react";
import { Box, Typography } from "@mui/material";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay } from "swiper/modules";
import "swiper/css";
import "./IndustriesSlider.scss";
import Image from "next/image";
import {
  IndustrySlider_img1,
  IndustrySlider_img2,
  IndustrySlider_img3,
  IndustrySlider_img4,
  IndustrySlider_img5,
  IndustrySlider_img6,
} from "@/public/index";

// Replace these with actual imported icon paths
const industries = [
  { label: "Industrial \n machinery", icon: IndustrySlider_img1 },
  { label: "Smart Homes & \n Buildings", icon: IndustrySlider_img2 },
  { label: "Sensor Teach \n Wearables", icon: IndustrySlider_img3 },
  { label: "Smart Metering", icon: IndustrySlider_img4 },
  { label: "Smart Farming", icon: IndustrySlider_img5 },
  { label: "Healthcare & \n Ventilation", icon: IndustrySlider_img6 }, // Use another image or add more imports as needed
];

// Duplicate industries array for seamless looping
const industriesLoop = [...industries, ...industries];

const IndustriesSlider = () => {
  return (
    <Box className="industries-wrapper">
      <Typography className="heading">Industries we work with</Typography>

      <Swiper
        modules={[Autoplay]}
        spaceBetween={60}
        slidesPerView="auto"
        loop
        autoplay={{
          delay: 1,
          disableOnInteraction: false,
          pauseOnMouseEnter: false,
        }}
        speed={5000}
        freeMode={{ enabled: true, momentum: false }}
        allowTouchMove={false}
        className="industries-swiper"
      >
        {industriesLoop.map((item, index) => (
          <SwiperSlide key={index} className="industry-slide">
            <Box className="industry-card">
              <Image
                src={item.icon}
                alt={item.label}
                className="icon"
                width={48}
                height={48}
              />
              <Typography className="label">
                {item.label.split("\n").map((line, i, arr) => (
                  <React.Fragment key={i}>
                    {line}
                    {i < arr.length - 1 && <br />}
                  </React.Fragment>
                ))}
              </Typography>
            </Box>
          </SwiperSlide>
        ))}
      </Swiper>
    </Box>
  );
};

export default IndustriesSlider;
