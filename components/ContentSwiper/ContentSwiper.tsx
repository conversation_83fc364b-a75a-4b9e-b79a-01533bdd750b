"use client";

import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay } from "swiper/modules";
import "swiper/css";
import {
  Box,
  Divider,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from "@mui/material";
import Image from "next/image";
import { ExpertiseIcon } from "@/public";
import "./ContentSwiper.scss";

interface TestCard {
  number: string;
  title: string;
  description: string | string[];
}

interface ContentSwiperProps {
  cards: TestCard[];
  listItems?: string[];
  heading: string;
  subtext: string;
}

const ContentSwiper = ({
  cards,
  listItems,
  heading,
  subtext,
}: ContentSwiperProps) => {
  return (
    <Box className="custom-iiot-section">
      {/* Left Content: List */}
      <Box className="iiot-left">
        <Typography
          className="iiot-heading"
          dangerouslySetInnerHTML={{ __html: heading }}
        />
        <Divider className="industries-iot-divider" />
        <Typography className="iiot-subtext">{subtext}</Typography>
        <List className="iiot-list">
          {listItems &&
            listItems.map((item, idx) => (
              <ListItem key={idx} className="iiot-list-item">
                <ListItemIcon>
                  <Image src={ExpertiseIcon} alt="icon" width={24} height={24} />
                </ListItemIcon>
                <ListItemText primary={item} />
              </ListItem>
            ))}
        </List>
      </Box>

      {/* Right Content: Swiper */}
      <Box className="iiot-right">
        <Box className="mechanical-testing-section">
          <Swiper
            modules={[Autoplay]}
            spaceBetween={24}
            slidesPerView={2}
            loop={true}
            autoplay={{ delay: 2500, disableOnInteraction: false }}
            style={{ padding: "0 20px 40px 20px" }}
          >
            {cards.map((item, index) => (
              <SwiperSlide key={index}>
                <div className="card">
                  <div className="number">{item.number}</div>
                  <div className="title">
                    {item.title}
                    <Divider
                      className="divider"
                      sx={{
                        borderColor: "#fff",
                        borderWidth: 1,
                        margin: "10px 0",
                      }}
                    />
                  </div>
                  <div className="text">
                    {Array.isArray(item.description) ? (
                      <List
                        sx={{
                          padding: 0,
                          listStyleType: "disc",
                          color: "#fff",
                          "& .MuiListItem-root": {
                            display: "list-item",
                            padding: 0,
                            marginBottom: "8px",
                          },
                          "& .MuiListItemText-primary": {
                            color: "#fff",
                            fontSize: "0.9rem",
                          },
                          "& .MuiListItem-root::marker": {
                            color: "#fff",
                            fontSize: "1.2rem",
                          },
                        }}
                      >
                        {item.description.map((desc, i) => (
                          <ListItem key={i}>
                            <ListItemText primary={desc} />
                          </ListItem>
                        ))}
                      </List>
                    ) : (
                      <Typography sx={{ color: "#fff", fontSize: "0.9rem" }}>
                        {item.description}
                      </Typography>
                    )}
                  </div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </Box>
      </Box>
    </Box>
  );
};

export default ContentSwiper;
