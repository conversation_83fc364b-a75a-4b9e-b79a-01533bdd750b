.custom-iiot-section {
  display: flex;
  align-items: flex-start;
  background-color: #021f2b;
  color: #ffffff;
  padding: 100px 80px;
  gap: 64px;

  .iiot-left {
    flex: 0.5;
    max-width: 395px;

    .iiot-heading {
      font-family: Montserrat !important;
      font-weight: 700;
      font-size: 32px;
      vertical-align: middle;
      margin-bottom: 20px;
    }

    .industries-iot-divider {
      border: 0.5px solid #ffffff !important;
      margin-bottom: 31px;
    }

    .iiot-subtext {
      font-family: Montserrat !important;
      font-weight: 500;
      font-size: 15px;
      letter-spacing: 0%;
      vertical-align: middle;
      max-width: 331px;
      margin-bottom: 52px;
    }

    .iiot-list {
      padding: 0;

      .iiot-list-item {
        align-items: flex-start;
        padding: 6px 0;

        .MuiListItemIcon-root {
          min-width: 30px;

          img {
            width: 18px;
            height: 20px;
          }
        }

        .MuiListItemText-primary {
          font-family: Montserrat !important;
          font-weight: 500;
          font-size: 15px;
          letter-spacing: 0%;
          vertical-align: middle;
        }
      }
    }
  }

  .iiot-right {
    flex: 0.5;
    max-width: 65%;

    .mechanical-testing-section {
      padding-bottom: 60px;

      .swiper-slide {
        display: flex;
        align-items: stretch;
        height: auto;
      }

      .card {
        background-color: #002233;
        padding: 31px;
        border-radius: 0.5rem;
        color: white;
        transition: background-color 0.3s ease, color 0.3s ease;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        height: 100%;
        min-height: 420px;
        box-sizing: border-box;
        border: 1px solid #d77d46 !important;

        &:hover {
          background-color: #d77d46;
          color: #ffffff;

          .text {
            color: #ffffff;
          }
        }

        .number {
          font-family: Montserrat !important;
          font-weight: 700;
          font-size: 96px;
          line-height: 100%;
          letter-spacing: 0%;
          vertical-align: middle;
          color: #ffffff;
          opacity: 15%;
        }

        .title {
          margin-top: 93px;
          font-family: Montserrat !important;
          font-weight: 700;
          font-size: 24px;
          line-height: 100%;
          letter-spacing: 0%;
          vertical-align: middle;
          margin-bottom: 35px;
          min-width: fit-content;
          color: #ffffff;
          text-align: left;
          width: 100%;
        }

        .text {
          font-size: 0.95rem;
          color: #ffffff;
        }
      }
    }
  }

  @media (max-width: 960px) {
    flex-direction: column;
    padding: 60px 24px;

    .iiot-left,
    .iiot-right {
      max-width: 100%;
    }

    .iiot-left {
      margin-bottom: 48px;
    }

    .iiot-right {
      .mechanical-testing-section {
        .swiper-slide {
          width: 100%;
        }
      }
    }
  }
}
