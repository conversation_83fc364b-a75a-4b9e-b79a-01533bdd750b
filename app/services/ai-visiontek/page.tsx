"use state";
import { Box, Typography, List, ListItem } from "@mui/material";
import Image from "next/image";
import "./AiVision.scss";
import {
  AI<PERSON><PERSON>,
  aivisionbanner,
  AIVisionTek,
  ClassIdentification,
  ExpertiseIcon,
  MotionDetection,
  PeopleConsulting,
  PlateRecognisation,
  ServiceIcon,
  VenomDigramImage,
} from "@/public";
import MechanicalTestingSwiper from "../mechanical-design/MechanicalTestingSwiper";
import Link from "next/link";

const AiVisionPage = () => {
  const data = [
    {
      number: "01",
      title: "Agentic AI Solutions",
      description:
        "Develop an Integrate personalized AI agents and virtual assistants to automate repetitive tasks and to simplify complex processes like Gener query assistance, data entry, work assistance with 24x7 availability to improve overall productivity.",
    },
    {
      number: "02",
      title: "Speech Recognition",
      description:
        "Decode spoken words into actionable insights with trained speech recognition engine. We automatic speech recognition (ASR) engines and natural language processing (NLP) to deliver accurate real-time transcription.",
    },
    {
      number: "03",
      title: "Camera Vision",
      description:
        "CMS platforms adds flexibility in any Start from security solutions to image processing techniques, our technologist uses computer vision techniques like image classification, facial recognition, and biometric authentication to extract meaningful information and analyzing the same for multiple applications",
    },
    {
      number: "04",
      title: "AI App Development",
      description:
        "Our AI experts can work with your team to analyzing the valuable information to make smarter business decisions. We can construct advanced algorithms for AI web Applications and mobile apps, and software solutions to anticipate the user challenges and can better satisfy your customers’ needs",
    },
    {
      number: "05",
      title: "Data Mining, Annotation, and Labeling ",
      description:
        "Our focused structural approach and data mining techniques with categorizing the data sets and image classification helps you to get better insight about your product and service performance. We can helps you to precisely train your model for predictive analysis.",
    },
    {
      number: "06",
      title: "Analysis",
      description:
        "Our experts can help you in utilization of your data to identify various patterns and critical information which can be very crucial for your business objectives. This information can be further analyzed to generate alarms and events for preventive actions.",
    },
  ];
  const AIVisionTekContent = {
    caseStudies: {
      title: "Case Studies",
      studies: [
        { title: "Motion Detection", image: MotionDetection },
        { title: "People Counting", image: PeopleConsulting },
        { title: "Class Identification", image: ClassIdentification },
        { title: "Plate Recognition", image: PlateRecognisation },
      ],
    },
  };
  return (
    <Box className="ai_vision-page-container">
      <Box className="ai_vision-page-content">
        <Box className="ai_vision-page-header">
          <Box className="ellipse-container">
            <Image src={aivisionbanner} alt="Ellipse" className="ellipse-image" />
          </Box>

          <Box className="hero-content" style={{}}>
            
          </Box>
        </Box>

        <Box className="gen-ai-section">
          <Box className="gen-ai-content">
            {/* Left Side */}
            <Box className="gen-ai-left">
              <Typography className="gen-ai-heading">
                Deploy your Scalable,
                <br />
                Reliable, and Secure
                <br />
                Gen AI Applications
              </Typography>
              <Image
                src={AIImage}
                alt="AI Head"
                width={300}
                height={300}
                className="gen-ai-image"
              />
            </Box>

            {/* Right Side */}
            <Box className="gen-ai-right">
              <Typography className="gen-ai-paragraph">
                At Aadvik Labs, our experts specialize in vector machines,
                logical frameworks, and real-time AI applications to build
                efficient, outcome-driven workflows. We leverage Machine
                Learning and Deep Learning to automate complex tasks, extract
                insights from large datasets, and enable precise
                decision-making.
              </Typography>
              <Typography className="gen-ai-paragraph">
                Passionate about advancing AI & ML technologies, and deep
                foundation in AI Models, our engineers can help you in training
                your models through live data integration. We build and deliver
                custom Gen AI based solutions that keep clients ahead in this
                rapidly evolving digital world.
              </Typography>
            </Box>
          </Box>
        </Box>

        <Box className="ai-tech-stack-section">
          <Typography className="section-title">
            AI /ML & Technology Stack
          </Typography>
          <Box className="ai-tech-stack-content">
            {/* Left Text Content */}
            <Box className="ai-tech-left">
              <Typography className="section-description">
                Aadvik TekLabs leverage the power of AI to revolutionize the way
                of problem solving approach. Our deep expertise in system design
                enables us to automate complex processes, extracting actionable
                insights from massive data sets, and enabling faster, more
                precise decision-making.
                <br />
                <br />
                By collaborating with our AI engineers, you gain access to
                advanced solutions tailored to optimize operations, enhance
                decision-making, and drive innovation across your business.
              </Typography>
            </Box>

            {/* Right Grid of Technology Items */}
            <Box className="ai-tech-right-grid">
              <Box className="ai-tech-card highlighted">
                <Box className="ai-tech-icon">
                  <Image src={ServiceIcon} alt="icon" width={43} height={49} />
                </Box>
                <Typography className="ai-tech-title">
                  OpenCV, MATLAB, TensorFlow
                </Typography>
              </Box>

              <Box className="ai-tech-card">
                <Box className="ai-tech-icon">
                  <Image src={ServiceIcon} alt="icon" width={43} height={49} />
                </Box>
                <Typography className="ai-tech-title">
                  Deep Learning : CNN, RNN, LSTM
                </Typography>
              </Box>

              <Box className="ai-tech-card">
                <Box className="ai-tech-icon">
                  <Image src={ServiceIcon} alt="icon" width={43} height={49} />
                </Box>
                <Typography className="ai-tech-title">
                  Traditional ML : SVM, Clustering
                </Typography>
              </Box>

              <Box className="ai-tech-card">
                <Box className="ai-tech-icon">
                  <Image src={ServiceIcon} alt="icon" width={43} height={49} />
                </Box>
                <Typography className="ai-tech-title">
                  Signal Processing on Linux, Yocto, & Android
                </Typography>
              </Box>
            </Box>
          </Box>
          <Box>
            <Typography className="section-title-what-we-offer">
              What we offer
            </Typography>
          </Box>
        </Box>

        {/* Card Scroll */}
        <Box className="ai-vision-swiper">
          <MechanicalTestingSwiper data={data} />
        </Box>

        <Box className="ai-solutions-section">
          <Box className="ai-solutions-container">
            {/* Left Content */}
            <Box className="ai-solutions-left">
              <Box className="ai-solutions-left-content">
                <Typography className="ai-solutions-heading">
                  We develop AI solutions that <br />
                  have delivered tangible results <br />
                  for our clients across a wide <br />
                  range of industries .
                </Typography>

                <Typography className="ai-solutions-link-text">
                  <Typography className="ai-solutions-link">
                    Here are a few success stories that showcase the power of
                    vision-based technology in action to deliver tangible
                    results for our clients across a wide range of industries.
                  </Typography>
                </Typography>
              </Box>
            </Box>

            {/* Right Side with Image */}
            <Box className="ai-solutions-right">
              <Image
                src={VenomDigramImage}
                alt="AI Delivered Solutions"
                className="solutions-image"
              />
            </Box>
          </Box>
        </Box>

        {/* Case Studies */}
        <Box className="case-studies-section">
          <Typography className="case-studies-title">
            {AIVisionTekContent.caseStudies.title}
          </Typography>

          <Box className="case-studies-row">
            {AIVisionTekContent.caseStudies.studies.map((study, index) => (
              <Box className="case-studies-card" key={index}>
                <Image
                  src={study.image}
                  alt={study.title}
                  className="case-studies-image"
                />
                <Box className="case-studies-content">
                  <Typography className="case-studies-label">
                    {study.title}
                  </Typography>
                </Box>
              </Box>
            ))}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default AiVisionPage;
