"use client";

import { useState } from "react";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import "./SystemTesting.scss";
import {
  systemtestingbanner,
  SystemTestingBanner,
  SystemTestingWhychooseus,
  ToolandPlatform,
} from "@/public";
import WhatWeOffer from "@/components/Whatweoffer/WhatWeOffer";
import SystemReliabilityDialog from "@/components/SystemReliabilityDialog/SystemReliabilityDialog";

const SystemTestingPage = () => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedService, setSelectedService] = useState<string | null>(null);
  const [contentType, setContentType] = useState<
    "system-reliability" | "communication" | "software" | "simulation"
  >("system-reliability");

  const services = [
    { title: "System Reliability Testing", type: "system-reliability" },
    { title: "Communication testing", type: "communication" },
    { title: "Simulation Testing", type: "simulation" },
    { title: "Software Testing", type: "software" },
    {
      title: "Environmental & Mechanical Endurance",
      type: "system-reliability",
    },
    { title: "Regulatory & Certification", type: "system-reliability" },
  ];

  // Handler to open dialog when a service is clicked
  const handleServiceClick = (service: any) => {
    setSelectedService(service.title);
    setContentType(service.type || "system-reliability");
    setDialogOpen(true);
  };

  // Handler to close dialog
  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedService(null);
  };

  return (
    <Box className="system-testing-page-container">
      <Box className="system-testing-page-content">
        <Box className="system-testing-page-header">
          <Box className="ellipse-container">
            <Image
              src={systemtestingbanner}
              alt="Ellipse"
              className="ellipse-image"
            />
            <Box className="banner-gradient" />
          </Box>
          <Box className="hero-content"></Box>
        </Box>

        {/* Testing & Compliance Section */}
        <Box className="testing-compliance-section">
          <Box className="testing-left">
            <Typography className="testing-headline">
              Aadvik TekLabs delivers future-ready embedded systems with
              rigorous testing and global compliance.
            </Typography>
          </Box>
          <Box className="testing-right">
            <Typography paragraph className="testing-text">
              At Aadvik TekLabs, we ensure every embedded system is not just
              connected, but intelligent, intuitive, and ready for the future.
            </Typography>
            <Typography paragraph className="testing-text">
              Our comprehensive testing services span functional validation,
              product reliability checks, and compliance with international
              standards such as ISO, IEC, UL, and CE. With a strong focus on
              safety and performance, our team conducts meticulous electrical
              and functional testing before any product leaves our facility.
            </Typography>
            <Typography paragraph className="testing-text">
              Backed by a dedicated quality assurance team, we guarantee that
              each system meets the highest benchmarks of reliability, safety,
              and global regulatory compliance.
            </Typography>
          </Box>
        </Box>

        {/* Specialization Section */}
        <Box className="specialization-section">
          <Typography component="h3" className="specialization-heading">
            Our specialization
          </Typography>
          <Typography className="specialization-body">
            Aadvik Engineers provide specialized support in reliability
            engineering and testing compliance with FCC, RoHS, and safety
            regulations, ensuring your product meets critical regulatory
            requirements and industry standards. We specialize in:
          </Typography>
        </Box>

        {/* What We Offer Section */}
        <Box className="what-we-offer-section">
          <WhatWeOffer
            services={services}
            onServiceClick={handleServiceClick}
          />
        </Box>
        <Box className="why-choose-us">
          <Image src={SystemTestingWhychooseus} alt="whychooseus" />
        </Box>

        <Box className="tools-platform-section">
          {/* Yellow card with image */}
          <Box className="tools-card">
            <Image
              src={ToolandPlatform}
              alt="Tools and Platforms for IoT Communication Testing"
              className="tools-image"
            />
          </Box>

          {/* Supporting caption */}
          <Typography className="tools-caption">
            <em>
              Aadvik TekLabs provides comprehensive hardware and software
              testing services, ensuring products meet industry standards,
              deliver top performance, ensure user safety, and are
              market-readiness of product.
            </em>
          </Typography>
        </Box>

        {/* System Reliability Dialog */}
        <SystemReliabilityDialog
          open={dialogOpen}
          onClose={handleCloseDialog}
          contentType={contentType}
        />
      </Box>
    </Box>
  );
};

export default SystemTestingPage;
0;
