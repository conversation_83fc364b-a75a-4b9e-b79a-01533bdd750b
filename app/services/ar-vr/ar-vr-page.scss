.ar_vr-page-container {
    .ar_vr-page-content {
        .ar_vr-page-header {
            display: flex;
            flex-direction: column;
            align-items: center;
            height: 100vh;
            width: 100%;

            .ellipse-container {
                width: 100%;
                height: 100vh;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .hero-content {
                .title {
                    font-family: Inter !important;
                    font-weight: 800;
                    font-size: 96px;
                    leading-trim: Cap height;
                    line-height: 100%;
                    letter-spacing: 0%;
                    text-align: left;
                    vertical-align: middle;
                    color: #97A3AA;
                    margin-top: 200px;
                    margin-bottom: 41px;
                    width: 100%;
                    display: flex;
                    justify-content: end;
                    text-align: left;
                    margin-right: 100px;

                    @media (max-width: 768px) {
                        font-size: 48px;
                        margin-top: 100px;
                    }

                }

                .header-image {
                    color: transparent;
                    position: absolute;
                    left: inherit;
                    bottom: 0;
                    margin-left: 81px;
                }
            }






        }

        .content {
            background-color: #FFFFFF;

            .ar_vr-section {
                padding-bottom: 100px;
                padding-top: 124px;
                background-color: #fff;
                color: #001219;

                .header-container {
                    display: flex;
                    flex-direction: column;
                    margin-bottom: 78px;

                    .image-container {
                        width: 100%;
                        margin-top: 98px;

                        img {
                            width: 100%;
                            height: auto;
                            object-fit: cover;
                        }
                    }
                }



                .title {
                    font-family: Inter !important;
                    font-weight: 800;
                    font-size: 48px;
                    line-height: 100%;
                    letter-spacing: 0%;
                    text-align: left;
                    margin-bottom: 50px;
                    padding-left: 95px;
                    padding-right: 95px;
                }

                .description-container {
                    padding-left: 95px;
                    padding-right: 95px;
                    display: flex;
                    justify-content: space-between;
                    gap: 296px;
                }



            }

            .tool-design-section {
                padding-left: 90px;
                padding-right: 90px;
                display: flex;
                justify-content: space-between;
                background-image: url('/images/ar-vr/tool-design-bg.png');

                .left-section {
                    max-width: 40%;

                    .title {
                        font-family: Inter !important;
                        font-weight: 800;
                        font-size: 48px;
                        leading-trim: Cap height;
                        line-height: 100%;
                        letter-spacing: 0%;
                        color: #021F2E;
                        margin-bottom: 60px;
                    }

                    .left-content {
                        display: flex;
                        flex-direction: column;
                        gap: 90px;

                        .left-sub-title {
                            font-family: Montserrat !important;
                            font-weight: 500;
                            font-size: 16px;
                            leading-trim: Cap height;
                            line-height: 100%;
                            letter-spacing: 0%;
                            color: #021F2E;
                        }
                    }
                }


                .right-section {
                    max-width: 60%;
                    margin-top: 34px;
                    margin-left: 129px;

                    .description-box {
                        padding: 62px 124px 59px 52px;
                        background: rgba(215, 125, 70, 0.19);
                        border-radius: 8px;
                        margin-bottom: 45px;



                        p {
                            font-family: Montserrat !important;
                            font-weight: 500;
                            font-size: 16px;
                            leading-trim: Cap height;
                            line-height: 100%;
                            letter-spacing: 0%;
                            color: #021F2E;
                        }
                    }

                    .cards {
                        margin-top: 60px;

                        .card {
                            max-width: 513px;
                            padding-left: 54px;
                            margin-bottom: 65px;
                            display: flex;
                            gap: 45px;

                            img {
                                margin-bottom: 20px;
                            }


                            .description {
                                font-family: Montserrat !important;
                                font-weight: 500;
                                font-size: 16px;
                                leading-trim: Cap height;
                                line-height: 100%;
                                letter-spacing: 0%;
                                color: #021F2E;
                            }
                        }
                    }
                }
            }


            .simulation-section {
                padding-left: 90px;
                padding-right: 90px;
                padding-bottom: 205px;

                .title {
                    font-family: Inter !important;
                    font-weight: 800;
                    font-size: 48px;
                    leading-trim: Cap height;
                    line-height: 100%;
                    letter-spacing: 0%;
                    color: #021F2E;
                    margin-bottom: 101px;
                }

                .simulation-content {
                    max-width: 40%;

                    p {
                        font-family: Montserrat !important;
                        font-weight: 500;
                        font-size: 16px;
                        leading-trim: Cap height;
                        line-height: 100%;
                        letter-spacing: 0%;
                        color: #021F2E;
                        margin-bottom: 25px;

                    }


                }

                .simulation-image-container {
                    padding: 28px;
                    border: 2px dotted #D77D46;
                    border-radius: 12px;

                    .text {
                        margin-top: 20px;
                        font-family: Poppins;
                        font-weight: 275;
                        font-size: 24px;
                        line-height: 100%;
                        letter-spacing: 0%;
                        text-align: center;
                        text-decoration: underline;
                        text-decoration-style: solid;
                        text-decoration-offset: 0%;
                        text-decoration-thickness: 0%;
                        text-decoration-skip-ink: auto;

                    }

                    &:hover {
                        cursor: pointer;
                        background-color: #D77D46;
                        transition: background-color 0.3s ease;

                        .text {
                            color: #fff;
                        }
                    }
                }
            }


        }
    }
}