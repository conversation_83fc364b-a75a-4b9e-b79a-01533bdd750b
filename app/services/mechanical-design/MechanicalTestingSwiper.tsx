"use client";

import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay } from "swiper/modules";
import "swiper/css";
import {
  Box,
  Divider,
  Typography,
  List,
  ListItem,
  ListItemText,
} from "@mui/material";
import "./MechanicalTestingSwiper.scss";

interface TestCard {
  number: string;
  title: string;
  description: string | string[];
}

const MechanicalTestingSwiper = ({ data }: { data: TestCard[] }) => (
  <Box className="mechanical-testing-section">
    <Swiper
      modules={[Autoplay]}
      spaceBetween={24}
      slidesPerView={1}
      loop={true}
      autoplay={{ delay: 2500, disableOnInteraction: false }}
      breakpoints={{
        600: { slidesPerView: 2 },
        900: { slidesPerView: 3 },
      }}
      style={{ padding: "0 20px 40px 20px" }}
    >
      {data.map((item, index) => (
        <SwiperSlide key={index}>
          <div className="card">
            <div className="number">{item.number}</div>
            <div className="title">
              {item.title}
              <Divider
                className="divider"
                sx={{ borderColor: "#fff", borderWidth: 1, margin: "10px 0" }}
              />
            </div>
            <div className="text">
              {Array.isArray(item.description) ? (
                <List
                  sx={{
                    padding: 0,
                    listStyleType: "disc",
                    color: "#fff",
                    "& .MuiListItem-root": {
                      display: "list-item",
                      padding: 0,
                      marginBottom: "8px",
                    },
                    "& .MuiListItemText-primary": {
                      color: "#fff",
                      fontSize: "0.9rem",
                    },
                    "& .MuiListItem-root::marker": {
                      color: "#fff",
                      fontSize: "1.2rem",
                    },
                  }}
                >
                  {item.description.map((desc, i) => (
                    <ListItem key={i}>
                      <ListItemText primary={desc} />
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography sx={{ color: "#fff", fontSize: "0.9rem" }}>
                  {item.description}
                </Typography>
              )}
            </div>
          </div>
        </SwiperSlide>
      ))}
    </Swiper>
  </Box>
);

export default MechanicalTestingSwiper;
