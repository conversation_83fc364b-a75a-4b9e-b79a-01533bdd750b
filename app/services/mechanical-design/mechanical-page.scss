.mechanical-page-container {
    .mechanical-page-content {
        .mechanical-page-header {
            display: flex;
            flex-direction: column;
            align-items: center;
            height: 100vh;
            width: 100%;

            .ellipse-container {
                width: 100%;
                height: 100vh;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .hero-content {
                .title {
                    font-family: Inter !important;
                    font-weight: 800;
                    font-size: 96px;
                    leading-trim: Cap height;
                    line-height: 100%;
                    letter-spacing: 0%;
                    text-align: center;
                    vertical-align: middle;
                    color: #97A3AA;
                    margin-top: 200px;
                    margin-bottom: 41px;

                }
            }






        }

        .content {
            background-color: #FFFFFF;

            .our-process-section {
                padding-bottom: 170px;
                padding-left: 70px;
                padding-right: 70px;

                .title {
                    font-family: Inter !important;
                    font-weight: 800;
                    font-size: 48px;
                    line-height: 100%;
                    letter-spacing: 0%;
                    text-align: center;
                    padding-bottom: 38px;
                    border-bottom: 1px solid #021F2E;
                    padding-bottom: 53px;

                }

                img {
                    width: 100%;
                    height: auto;
                    object-fit: cover;
                    margin-top: 50px;
                }
            }

            .tool-design-section {
                padding-left: 90px;
                padding-right: 90px;
                display: flex;
                justify-content: space-between;

                .left-section {
                    max-width: 40%;

                    .title {
                        font-family: Inter !important;
                        font-weight: 800;
                        font-size: 48px;
                        leading-trim: Cap height;
                        line-height: 100%;
                        letter-spacing: 0%;
                        color: #021F2E;
                        margin-bottom: 60px;
                    }

                    .left-sub-title {
                        font-family: Montserrat !important;
                        font-weight: 500;
                        font-size: 16px;
                        leading-trim: Cap height;
                        line-height: 100%;
                        letter-spacing: 0%;
                        color: #021F2E;
                    }
                }


                .right-section {
                    max-width: 60%;
                    margin-top: 34px;
                    margin-left: 129px;

                    .description-box {
                        padding: 62px 124px 59px 52px;
                        background: rgba(215, 125, 70, 0.19);
                        border-radius: 8px;
                        margin-bottom: 45px;



                        p {
                            font-family: Montserrat !important;
                            font-weight: 500;
                            font-size: 16px;
                            leading-trim: Cap height;
                            line-height: 100%;
                            letter-spacing: 0%;
                            color: #021F2E;
                        }
                    }

                    .cards {
                        .card {
                            max-width: 513px;
                            padding-left: 54px;
                            margin-bottom: 65px;

                            img {
                                margin-bottom: 20px;
                            }

                            .title {
                                font-family: Montserrat !important;
                                font-weight: 600;
                                font-size: 24px;
                                line-height: 100%;
                                letter-spacing: 0%;
                                vertical-align: middle;
                                color: #030303;
                                margin-bottom: 25px;
                            }

                            .description {
                                font-family: Montserrat !important;
                                font-weight: 500;
                                font-size: 16px;
                                leading-trim: Cap height;
                                line-height: 100%;
                                letter-spacing: 0%;
                                color: #021F2E;
                            }
                        }
                    }
                }
            }


            .simulation-section {
                padding-left: 90px;
                padding-right: 90px;
                padding-bottom: 205px;

                .title {
                    font-family: Inter !important;
                    font-weight: 800;
                    font-size: 48px;
                    leading-trim: Cap height;
                    line-height: 100%;
                    letter-spacing: 0%;
                    color: #021F2E;
                    margin-bottom: 101px;
                }

                .simulation-content {
                    max-width: 40%;

                    p {
                        font-family: Montserrat !important;
                        font-weight: 500;
                        font-size: 16px;
                        leading-trim: Cap height;
                        line-height: 100%;
                        letter-spacing: 0%;
                        color: #021F2E;
                        margin-bottom: 25px;

                    }
                }
            }

            .how-we-work-section {
                padding-left: 95px;
                padding-right: 95px;
                padding-bottom: 400px;
                display: flex;
                justify-content: space-between;

                .left-section {
                    max-width: 40%;

                    .title {
                        font-family: Inter !important;
                        font-weight: 800;
                        font-size: 48px;
                        line-height: 100%;
                        letter-spacing: 0%;
                        color: #021F2E;
                        margin-bottom: 110px;
                    }

                    .description {
                        font-family: Montserrat !important;
                        font-weight: 500;
                        font-size: 16px;
                        line-height: 100%;
                        letter-spacing: 0%;
                        color: #021F2E;
                        margin-bottom: 25px;
                    }
                }
            }
        }
    }
}