.mechanical-testing-section {
  padding-bottom: 60px;

  .title {
    font-family: Inter !important;
    font-weight: 800;
    font-size: 48px;
    letter-spacing: 0%;
    color: #021f2e;
    text-align: center;
    margin-bottom: 92px;
  }

  .swiper-slide {
    display: flex;
    align-items: stretch;
    height: auto;
  }

  .card {
    background-color: #002233;
    padding: 31px;
    border-radius: 0.5rem;
    color: white;
    transition: background-color 0.3s ease, color 0.3s ease;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    height: 100%;
    min-height: 420px;
    box-sizing: border-box;
    border: 1px solid #d77d46 !important;

    &:hover {
      background-color: #d77d46;
      color: #ffffff;

      .text {
        color: #ffffff;
      }
    }

    .number {
      font-family: Montserrat !important;
      font-weight: 700;
      font-size: 96px;
      line-height: 100%;
      letter-spacing: 0%;
      vertical-align: middle;
      color: #ffffff;
      opacity: 15%;
    }

    .title {
      margin-top: 93px;
      font-family: Montserrat !important;
      font-weight: 700;
      font-size: 24px;
      line-height: 100%;
      letter-spacing: 0%;
      vertical-align: middle;
      // text-decoration: underline;
      // text-underline-offset: 15px;
      margin-bottom: 35px;
      min-width: fit-content;
      color: #ffffff;
      text-align: left;
      width: 100%;
    }

    .text {
      font-size: 0.95rem;
      color: #ffffff;
    }
  }
}
