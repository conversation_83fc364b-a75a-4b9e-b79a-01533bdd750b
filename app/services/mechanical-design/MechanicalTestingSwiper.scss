.mechanical-testing-section {
  padding-bottom: 60px;

  .title {
    font-family: Inter !important;
    font-weight: 800;
    font-size: 48px;
    letter-spacing: 0%;
    color: #021f2e;
    text-align: center;
    margin-bottom: 92px;
  }

  .swiper-slide {
    display: flex;
    align-items: stretch;
    height: auto;
  }

  .card {
    background-color: #002233;
    padding: 17px 28px;
    border-radius: 8.98px;
    color: white;
    transition: background-color 0.3s ease, color 0.3s ease;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    width: 405px;
    height: 490px;
    min-width: 405px;
    min-height: 490px;
    max-width: 100%;
    box-sizing: border-box;
    border: 0.56px solid #d77d46 !important;
    opacity: 1;

    // Use CSS variable for hover color
    &:hover {
      background-color: var(--card-hover-bg, #d77d46);
      color: var(--card-hover-text, #ffffff);

      .title,
      .divider,
      .description,
      .text {
        color: var(--card-hover-text, #ffffff) !important;
        border-color: var(--card-hover-text, #ffffff) !important;
      }
    }

    .number {
      font-family: Montserrat !important;
      font-weight: 700;
      font-size: 96px;
      line-height: 100%;
      letter-spacing: 0%;
      vertical-align: middle;
      color: #ffffff;
      opacity: 15%;
    }

    .title {
      margin-top: 93px;
      font-family: Montserrat !important;
      font-weight: 700;
      font-size: 24px;
      line-height: 100%;
      letter-spacing: 0%;
      vertical-align: middle;
      // text-decoration: underline;
      // text-underline-offset: 15px;
      margin-bottom: 35px;
      min-width: fit-content;
      color: #ffffff;
      text-align: left;
      width: 100%;
    }

    .text {
      font-family: Montserrat !important;
      font-weight: 500;
      font-style: Medium;
      font-size: 15px;
      letter-spacing: 0%;
      vertical-align: middle;
    }
  }
}
