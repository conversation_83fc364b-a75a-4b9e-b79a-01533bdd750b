.platform-page-container {
  .platform-page-content {
    .platform-page-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      min-height: 100vh;
      height: 100%;
      width: 100%;

      .ellipse-container {
        width: 100%;
        min-height: 100vh;
        height: 100%;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .title {
        padding-top: 180px;
        font-family: Inter;
        font-weight: 800;
        font-size: 96px;
        line-height: 100%;
        letter-spacing: 0%;
        text-align: center;
        color: #97a3aa;
        margin-bottom: 41px;
      }

      .text-divider {
        color: #ffffff !important; // text color
        border-color: #ffffff !important; // divider line color
        font-family: Inter !important;
        font-weight: 800;
        font-size: 24px;
        line-height: 100%;
        letter-spacing: 0%;
        text-align: center;
        // margin-bottom: 101px;
      }
      .hero-content {
        position: relative;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        z-index: 2;
        color: #fff;
        text-align: center;
        justify-content: center;
      }

      .hero-image {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 0 auto;
      }
    }

    .about-section {
      background-color: #ffffff;
      padding: 120px 100px 125px 82px;

      .title {
        font-family: Inter !important;
        font-weight: 700;
        font-size: 40px;
        line-height: 100%;
        letter-spacing: 0%;
        color: #021f2e;
        margin-bottom: 95px;
      }

      .description-section {
        display: flex;
        gap: 73px;
        // justify-content: space-between;

        .description {
          font-family: Inter !important;
          font-weight: 700;
          font-size: 48px;
          letter-spacing: 0%;
          max-width: 800px;

          span {
            font-family: Inter !important;
            font-weight: 700;
            font-size: 48px;
            line-height: 100%;
            letter-spacing: 0%;
            color: #d77d46;
          }
        }

        .description-2 {
          max-width: 400px;
          font-family: Montserrat !important;
          font-weight: 500;
          font-size: 16px;
          letter-spacing: 0%;
          color: #021f2e;
          margin-bottom: 20px;

          span {
            font-family: Montserrat;
            font-weight: 700;
            font-size: 16px;
            line-height: 100%;
            letter-spacing: 0%;
          }
        }
      }
    }

    .feature-section {
      background-color: #ffffff;
      padding-left: 90px;
      padding-right: 90px;

      .title {
        padding-bottom: 15px;
        font-family: Inter !important;
        font-weight: 700;
        font-size: 40px;
        line-height: 100%;
        letter-spacing: 0%;
        text-align: center;
        color: #021f2e;
        border-bottom: 1px solid #021f2e;
      }

      .feature-grid {
        padding-left: 80px;
        padding-right: 80px;
        padding-top: 79px;
        padding-bottom: 157px;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        justify-items: center;
        align-items: stretch;
      }

      .feature-item {
        border-radius: 16px;
        // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 24px;
        padding-bottom: 0px;
        min-width: 0;
        min-height: 260px;
        width: 100%;
        max-width: 340px;
        box-sizing: border-box;
        gap: 25px;
      }

      .item-title {
        font-family: Montserrat !important;
        font-weight: 700;
        font-size: 20px;
        line-height: 100%;
        letter-spacing: 0%;
        vertical-align: middle;
        color: #d77d46;
        margin-bottom: 0;
      }

      .feature-icon {
        object-fit: contain;
      }
    }

    .connect-section {
      background-color: #ffffff !important;

      .title {
        font-family: Montserrat !important;
        font-weight: 500;
        font-size: 26px;
        letter-spacing: 0%;
        text-align: center;
        vertical-align: middle;
        color: #021f2e;
        padding-top: 100px;
      }

      .subtext {
        display: none;
      }

      .connect-container {
        background-color: #ffffff;
        padding-bottom: 110px;

        .content {
          .left-content {
            h2 {
              color: #021f2e;
              font-family: Inter !important;
              font-weight: 700;
              font-size: 40px;
              line-height: 100%;
              letter-spacing: 0%;
              max-width: 474px;
            }
          }

          .right-form {
            .form-input {
              input {
                background-color: #ffffff;
                color: #021f2e;
              }
            }
          }
        }
      }
    }
  }
}

@media (min-width: 1200px) and (max-width: 1600px) {
  .platform-page-container {
    .platform-page-content {
      .platform-page-header {
        display: flex;
        flex-direction: column;
        align-items: center;
        min-height: 100vh;
        height: 100%;
        width: 100%;

        .ellipse-container {
          width: 100%;
          min-height: 100vh;
          height: 100%;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .title {
          padding-top: 220px;
          font-family: Inter !important;
          font-weight: 800;
          font-size: 80px;
          line-height: 100%;
          letter-spacing: 0%;
          text-align: center;
          color: #97a3aa;
          margin-bottom: 41px;
        }

        .text-divider {
          color: #ffffff !important; // text color
          border-color: #ffffff !important; // divider line color
          font-family: Inter !important;
          font-weight: 800;
          font-size: 24px;
          line-height: 100%;
          letter-spacing: 0%;
          text-align: center;
          // margin-bottom: 101px;
        }
        .hero-content {
          position: relative;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          z-index: 2;
          color: #fff;
          text-align: center;
          justify-content: center;
        }
        .platform-image-container {
          margin-top: 60px;
          .hero-image {
            width: 888px;
            height: 550px;
            display: block;
            margin: 0 auto;
          }
        }
      }

      .about-section {
        background-color: #ffffff;
        padding: 120px 100px 125px 82px;

        .title {
          font-family: Inter !important;
          font-weight: 700;
          font-size: 40px;
          line-height: 100%;
          letter-spacing: 0%;
          color: #021f2e;
          margin-bottom: 95px;
        }

        .description-section {
          display: flex;
          gap: 73px;
          // justify-content: space-between;

          .description {
            font-family: Inter !important;
            font-weight: 700;
            font-size: 48px;
            letter-spacing: 0%;
            max-width: 800px;

            span {
              font-family: Inter !important;
              font-weight: 700;
              font-size: 48px;
              line-height: 100%;
              letter-spacing: 0%;
              color: #d77d46;
            }
          }

          .description-2 {
            max-width: 400px;
            font-family: Montserrat !important;
            font-weight: 500;
            font-size: 16px;
            letter-spacing: 0%;
            color: #021f2e;
            margin-bottom: 20px;

            span {
              font-family: Montserrat;
              font-weight: 700;
              font-size: 16px;
              line-height: 100%;
              letter-spacing: 0%;
            }
          }
        }
      }

      .feature-section {
        background-color: #ffffff;
        padding-left: 90px;
        padding-right: 90px;

        .title {
          padding-bottom: 15px;
          font-family: Inter !important;
          font-weight: 700;
          font-size: 40px;
          line-height: 100%;
          letter-spacing: 0%;
          text-align: center;
          color: #021f2e;
          border-bottom: 1px solid #021f2e;
        }

        .feature-grid {
          padding-left: 80px;
          padding-right: 80px;
          padding-top: 79px;
          padding-bottom: 157px;
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          justify-items: center;
          align-items: stretch;
        }

        .feature-item {
          border-radius: 16px;
          // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 40px 24px;
          padding-bottom: 0px;
          min-width: 0;
          min-height: 260px;
          width: 100%;
          max-width: 340px;
          box-sizing: border-box;
          gap: 25px;
        }

        .item-title {
          font-family: Montserrat !important;
          font-weight: 700;
          font-size: 20px;
          line-height: 100%;
          letter-spacing: 0%;
          vertical-align: middle;
          color: #d77d46;
          margin-bottom: 0;
        }

        .feature-icon {
          object-fit: contain;
        }
      }

      .connect-section {
        background-color: #ffffff !important;

        .title {
          font-family: Montserrat !important;
          font-weight: 500;
          font-size: 26px;
          letter-spacing: 0%;
          text-align: center;
          vertical-align: middle;
          color: #021f2e;
          padding-top: 100px;
        }

        .subtext {
          display: none;
        }

        .connect-container {
          background-color: #ffffff;
          padding-bottom: 110px;

          .content {
            .left-content {
              h2 {
                color: #021f2e;
                font-family: Inter !important;
                font-weight: 700;
                font-size: 40px;
                line-height: 100%;
                letter-spacing: 0%;
                max-width: 474px;
              }
            }

            .right-form {
              .form-input {
                input {
                  background-color: #ffffff;
                  color: #021f2e;
                }
              }
            }
          }
        }
      }
    }
  }
}
