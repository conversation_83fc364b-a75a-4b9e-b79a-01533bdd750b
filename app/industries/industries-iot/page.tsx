"use state";
import {
  Box,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography,
} from "@mui/material";
import Image from "next/image";
import "./IndustriesIot.scss";
import { ExpertiseIcon, IndustriesIotbanner, IndustryImg } from "@/public";
import MechanicalDesign from "@/components/MechanicalDesign/MechanicalDesign";
import MechanicalTestingSwiper from "@/app/services/mechanical-design/MechanicalTestingSwiper";

const IndustriesIotPage = () => {
  const data = [
    {
      number: "01",
      title: "Environmental Parameter Controls (EPC)",
      description: [
        "Control Systems Design for sensors & Actuators & other Env Sensors",
        "Integrating Env Sensors like Temperature, Humidity, photo detectors, Motion Sensors",
        "User Interface & Interaction System design",
      ],
    },
    {
      number: "02",
      title: "Wireless Connectivity Ennoblement",
      description: [
        "Communication channel establishment like WI-Fi, BLE, Zigbee, and  thread",
        "Communication Protocol development",
        "Communication Module development",
        "Signal strength & Range testing",
      ],
    },
    {
      number: "03",
      title: "Network Stack Integration",
      description: [
        "UI/UX design & development for HVAC system controls and maintenance",
        "Graphical Visualization & Device Controls",
        "Push Notifications & Alerts Systems",
        "Zones & Rooms design & Control",
        "App development for Fault Diagnostics",
      ],
    },
    {
      number: "04",
      title: "Predictive Maintenance & Remote Monitoring",
      description: [
        "System Energy Efficiency Calculations",
        "Data Collection and Analytics",
        "Scalability & Interoperability Testing",
        "Track maintenance schedules",
        "Optimized Inventory Management",
        "App development for field debugging and Maintenance",
      ],
    },
    {
      number: "05",
      title: "Integration with BMS & Home Automation",
      description: [
        "Automates centralize control design",
        "System connectivity with BACnet Protocols",
        "Connectivity with other building systems like security, fire alarms",
        "Energy management, and other critical building functions",
      ],
    },
    {
      number: "06",
      title: "Firmware Development & OTA Support",
      description: [
        "Firmware Development for control system operations and FOTA Support",
        "Communication Protocol development & Integration support",
        "Enabling Smart Features for EPC Controls",
      ],
    },
  ];
  return (
    <Box className="industries-iot-page-container">
      <Box className="industries-iot-page-content">
        <Box className="industries-iot-page-header">
          <Box className="ellipse-container">
            <Image
              src={IndustriesIotbanner}
              alt="Ellipse"
              className="ellipse-image"
            />
          </Box>
        </Box>
        <Box className="industries-iot-feature-section">
          {/* Left Column – Orange Heading */}
          <Box className="feature-left">
            <Typography component="h2" className="feature-heading-orange">
              Enabling OEMs to build <br />
              intelligent, connected, and <br />
              future-ready industrial <br />
              systems.
            </Typography>
          </Box>

          {/* Right Column – Descriptive Text */}
          <Box className="feature-right">
            <Typography variant="body1" className="feature-text">
              Aadvik TekLabs simplifies the path to connected manufacturing with
              an engineering-first approach tailored for OEMs. We deliver
              scalable IIoT solutions—from HMI systems and connected sensors to
              predictive maintenance, cloud integration, and secure remote
              monitoring.
            </Typography>
            <Typography variant="body1" className="feature-text">
              As Industry 4.0 transforms industrial design, our expertise in 5G,
              edge computing, AI, and AR helps OEMs build smarter,
              energy-efficient machines that reduce costs and extend asset life.
              We also tackle integration and interoperability challenges,
              ensuring reliable, future-ready systems with real-time insights
              and enhanced service capabilities.
            </Typography>
          </Box>
        </Box>
        {/* Industries4.0 */}
        <Box className="industries-iot-industry4-section">
          <Box className="industry4-title-container">
            <Typography className="industry4-title">
              We don’t just design smart products — we enable OEMs to build
              connected ecosystems with confidence.
            </Typography>
          </Box>
          <Box className="industry4-image-wrapper">
            <Image
              src={IndustryImg}
              alt="Industry 4.0 Infographic"
              className="industry4-image"
            />
          </Box>
        </Box>
        {/* Swiper section */}
        <Box className="custom-iiot-section">
          {/* Left Content */}
          <Box className="iiot-left">
            <Typography className="iiot-heading">
              Why Do We Need a <br /> Custom IIoT Solutions?
            </Typography>
            <Divider className="industries-iot-divider" />
            <Typography className="iiot-subtext">
              From sensors to the cloud, we design IIoT systems that fit into
              your operations, not the other way around.
            </Typography>

            {/* Bullet List */}
            <List className="iiot-list">
              {[
                "Capture and analyze data in real time",
                "Enable predictive and remote diagnostics",
                "Integrate effortlessly into complex industrial environments",
                "Scale securely with enterprise-ready cloud and edge technologies",
              ].map((item, idx) => (
                <ListItem key={idx} className="iiot-list-item">
                  <ListItemIcon>
                    <Image
                      src={ExpertiseIcon}
                      alt="icon"
                      width={24}
                      height={24}
                    />
                  </ListItemIcon>
                  <ListItemText primary={item} />
                </ListItem>
              ))}
            </List>
          </Box>

          {/* Right: Swiper Card Component */}
          <Box className="iiot-right">
            <Box className="swiper-section-industries-iot">
              <MechanicalTestingSwiper data={data} />
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default IndustriesIotPage;
