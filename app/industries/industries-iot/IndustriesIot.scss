.industries-iot-page-container {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  overflow: hidden;

  .industries-iot-page-content {
    width: 100vw;
    min-height: 100vh;
    position: relative;

    .industries-iot-page-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      width: 100vw;
      position: relative;

      .ellipse-container {
        width: 100vw;
        height: 100vh;
        position: relative;
        z-index: 1;

        .banner-gradient {
          position: absolute;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          pointer-events: none;
          background: linear-gradient(
            180deg,
            rgba(0, 0, 0, 0.7) 0%,
            rgba(0, 0, 0, 0.5) 100%
          );
          z-index: 2;
        }

        img {
          width: 100vw;
          height: 100vh;
          object-fit: cover;
          display: block;
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
        }
      }

      .hero-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 999px;
        height: 124px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 3;
        color: #fff;
        text-align: center;
        padding-bottom: 0;

        .title {
          font-family: Inter !important;
          font-weight: 800;
          font-style: Extra Bold;
          font-size: 64px;
          line-height: 100%;
          letter-spacing: 0%;
          text-align: center;
          vertical-align: middle;
          width: 100%;
          height: 100%;
          margin: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #d4d8da;
        }
      }
    }
    .industries-iot-feature-section {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 95px 222px 97px 94px;
      background-color: #ffffff;

      .feature-left {
        flex: 1;
        padding-right: 40px;

        .feature-heading-orange {
          font-family: Montserrat !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 30px;
          letter-spacing: 0%;
          color: #d77d46;
        }
      }

      .feature-right {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 24px;

        .feature-text {
          max-width: 482px;
          font-family: Montserrat !important;
          font-weight: 400;
          font-style: Regular;
          font-size: 16px;
          letter-spacing: 0%;
          color: #021f2e;
        }
      }

      @media (max-width: 960px) {
        flex-direction: column;
        padding: 60px 24px;

        .feature-left {
          padding-right: 0;
          margin-bottom: 32px;

          .feature-heading-orange {
            font-size: 24px;
            text-align: center;
          }
        }

        .feature-right {
          .feature-text {
            text-align: center;
            font-size: 15px;
          }
        }
      }
    }
    .industries-iot-industry4-section {
      padding: 80px 40px 120px;
      background-color: #ffffff;
      text-align: center;

      .industry4-title-container {
        display: flex;
        justify-content: center;
        .industry4-title {
          max-width: 1020px;
          font-family: Montserrat !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 30px;
          letter-spacing: 0%;
          text-align: center;
          color: #d77d46;
          margin-bottom: 60px;

          /* wider screens: a bit bigger */
          @media (min-width: 960px) {
            font-size: 32px;
          }
        }
      }

      .industry4-image-wrapper {
        display: inline-block;

        .industry4-image {
          width: 100%;
          height: auto;
          display: block;
        }
      }

      @media (max-width: 600px) {
        padding: 60px 16px;

        .industry4-title {
          font-size: 22px;
          margin-bottom: 40px;
        }
      }
    }
    .custom-iiot-section {
      display: flex;
      align-items: flex-start;
      background-color: #021f2b;
      color: #ffffff;
      padding: 100px 80px;
      gap: 64px;

      .iiot-left {
        flex: 0.5;
        max-width: 395px;

        .iiot-heading {
          font-family: Montserrat !important;
          font-weight: 700;
          font-style: Bold;
          font-size: 32px;
          vertical-align: middle;

          margin-bottom: 20px;
        }
        .industries-iot-divider {
          border: 0.5px solid #ffffff !important;
          margin-bottom: 31px;
        }

        .iiot-subtext {
          font-family: Montserrat !important;
          font-weight: 500;
          font-style: Medium;
          font-size: 15px;
          letter-spacing: 0%;
          vertical-align: middle;
          max-width: 331px;
          margin-bottom: 52px;
        }

        .iiot-list {
          padding: 0;

          .iiot-list-item {
            align-items: flex-start;
            padding: 6px 0;

            .MuiListItemIcon-root {
              min-width: 30px;

              img {
                width: 18px;
                height: 20px;
              }
            }

            .MuiListItemText-primary {
              font-family: Montserrat !important;
              font-weight: 500;
              font-style: Medium;
              font-size: 15px;
              letter-spacing: 0%;
              vertical-align: middle;
            }
          }
        }
      }

      .iiot-right {
        flex: 0.5;

        .swiper-section-industries-iot {
          width: 90%;
        }
      }

      @media (max-width: 960px) {
        flex-direction: column;
        padding: 60px 24px;

        .iiot-left,
        .iiot-right {
          width: 100%;
        }

        .iiot-left {
          margin-bottom: 48px;
        }
      }
    }
  }
}
